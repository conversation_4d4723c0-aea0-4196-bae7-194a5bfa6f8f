import { ActionCard } from '@components/actionCard/ActionCard';
import { Button } from '@components/ui/button';
import { LOCIZE_INSURANCE_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useCancelInsurance } from '@entities/insurance/hooks/useCancelInsurance';
import CheckRoundedIcon from '@icons/check-rounded.svg?react';
import { getRouteApi, Link } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

const routeApi = getRouteApi('/_protected/insurance-cancellation');

export const InsurancePageCancellation = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.insurance);
  const { isSuccess, isCancellation } = routeApi.useSearch();
  const { cancelInsurance, isPending } = useCancelInsurance();

  const onCancelInsurance = () => {
    cancelInsurance();
  };

  if (isCancellation) {
    return (
      <ActionCard
        title={t(LOCIZE_INSURANCE_KEYS.insuranceCancellationTitle)}
        description={t(LOCIZE_INSURANCE_KEYS.insuranceCancellationDescription)}
        after={
          <>
            <Button fullWidth asChild>
              <Link replace to={ROUTE_NAMES.insurance}>
                {t(LOCIZE_INSURANCE_KEYS.cancelBtn)}
              </Link>
            </Button>
            <Button
              loading={isPending}
              fullWidth
              className="mt-4"
              variant="grey"
              onClick={onCancelInsurance}
            >
              {t(LOCIZE_INSURANCE_KEYS.confirmBtn)}
            </Button>
          </>
        }
      />
    );
  }

  if (isSuccess) {
    return (
      <ActionCard
        title={t(LOCIZE_INSURANCE_KEYS.insuranceCancellationSuccessTitle)}
        icon={<CheckRoundedIcon />}
        description={t(
          LOCIZE_INSURANCE_KEYS.insuranceCancellationSuccessDescription,
        )}
        after={
          <Button fullWidth asChild>
            <Link replace to={ROUTE_NAMES.insurance}>
              {t(LOCIZE_INSURANCE_KEYS.closeBtn)}
            </Link>
          </Button>
        }
      />
    );
  }

  return null;
};
