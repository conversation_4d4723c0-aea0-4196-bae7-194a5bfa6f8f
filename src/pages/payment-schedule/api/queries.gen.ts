/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../shared/types/api.gen';

import {
  useQuery,
  useSuspenseQuery,
  UseQueryOptions,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';
import { fetcher } from '@lib/fetcher';
export type PaymentScheduleApplicationQueryVariables = Types.Exact<{
  referenceKey: Types.Scalars['String']['input'];
}>;

export type PaymentScheduleApplicationQuery = {
  application_by_reference?: {
    id: number;
    schedule_type: Types.ApplicationScheduleType;
    merchant_id?: number | null;
    installments?: Array<{
      id?: number | null;
      paid: number;
      due_at: string;
      amount: number;
      type?: Types.InstallmentinstallmentDate | null;
    } | null> | null;
    merchant?: {
      logo_path?: string | null;
      name: string;
      campaign?: { converting_schedule_name?: string | null } | null;
    } | null;
  } | null;
};

export const PaymentScheduleApplicationDocument = `
    query PaymentScheduleApplication($referenceKey: String!) {
  application_by_reference(reference_key: $referenceKey) {
    id
    schedule_type
    installments {
      id
      paid
      due_at
      amount
      type
    }
    merchant_id
    merchant {
      logo_path
      name
      campaign {
        converting_schedule_name
      }
    }
  }
}
    `;

export const usePaymentScheduleApplicationQuery = <
  TData = PaymentScheduleApplicationQuery,
  TError = unknown,
>(
  variables: PaymentScheduleApplicationQueryVariables,
  options?: Omit<
    UseQueryOptions<PaymentScheduleApplicationQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseQueryOptions<
      PaymentScheduleApplicationQuery,
      TError,
      TData
    >['queryKey'];
  },
) => {
  return useQuery<PaymentScheduleApplicationQuery, TError, TData>({
    queryKey: ['PaymentScheduleApplication', variables],
    queryFn: fetcher<
      PaymentScheduleApplicationQuery,
      PaymentScheduleApplicationQueryVariables
    >(PaymentScheduleApplicationDocument, variables),
    ...options,
  });
};

usePaymentScheduleApplicationQuery.getKey = (
  variables: PaymentScheduleApplicationQueryVariables,
) => ['PaymentScheduleApplication', variables];

export const useSuspensePaymentScheduleApplicationQuery = <
  TData = PaymentScheduleApplicationQuery,
  TError = unknown,
>(
  variables: PaymentScheduleApplicationQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<PaymentScheduleApplicationQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseSuspenseQueryOptions<
      PaymentScheduleApplicationQuery,
      TError,
      TData
    >['queryKey'];
  },
) => {
  return useSuspenseQuery<PaymentScheduleApplicationQuery, TError, TData>({
    queryKey: ['PaymentScheduleApplicationSuspense', variables],
    queryFn: fetcher<
      PaymentScheduleApplicationQuery,
      PaymentScheduleApplicationQueryVariables
    >(PaymentScheduleApplicationDocument, variables),
    ...options,
  });
};

useSuspensePaymentScheduleApplicationQuery.getKey = (
  variables: PaymentScheduleApplicationQueryVariables,
) => ['PaymentScheduleApplicationSuspense', variables];

usePaymentScheduleApplicationQuery.fetcher = (
  variables: PaymentScheduleApplicationQueryVariables,
  options?: RequestInit['headers'],
) =>
  fetcher<
    PaymentScheduleApplicationQuery,
    PaymentScheduleApplicationQueryVariables
  >(PaymentScheduleApplicationDocument, variables, options);
