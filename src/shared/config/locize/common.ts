export const LOCIZE_COMMON_KEYS = {
  logout: 'common.logout',
  anotherAuthMethod: 'another-auth-method',
  invoiceNeutralText: 'common.invoiceNeutralText',
  invoiceWarningText: 'common.invoiceWarningText',
  invoiceOverdueText: 'common.invoiceOverdueText',
  footerText: 'footer.text',
  monthLabel: 'month-label',
  emailPlaceholder: 'email-placeholder',
  leaveWithoutSavingPrompt: 'leave-without-saving-prompt',
  applyButton: 'apply-button',
  close: 'close',
} as const;

export const LOCIZE_INSTALLMENT_TYPE_TRANSLATION_KEYS: Record<string, string> =
  {
    CHARGE: 'installment-type.charge',
    REMINDER: 'installment-type.reminder',
    MONTHLY_INTEREST: 'installment-type.monthly-interest',
    MONTHLY_PRINCIPAL: 'installment-type.monthly-principal',
    DOWN_PRINCIPAL: 'installment-type.down-principal',
    CARD_PAYMENT_FEE: 'installment-type.card-payment-fee',
    CONTRACT_EDITING_FEE: 'installment-type.contract-editing-fee',
    CREDIT_LIMIT_RECALCULATION_FEE:
      'installment-type.credit-limit-recalculation-fee',
    DOWN_CONTRACT_FEE: 'installment-type.down-contract-fee',
    MONTHLY_CONTRACT_FEE: 'installment-type.monthly-contract-fee',
    MONTHLY_MANAGEMENT_FEE: 'installment-type.monthly-management-fee',
    WITHDRAWAL_FEE: 'installment-type.withdrawal-fee',
    PAYMENT_LEAVE_FEE: 'installment-type.payment-leave-fee',
    CREDIT_SCORING_FEE: 'installment-type.credit-scoring-fee',
    INSTANT_PAYMENT_FEE: 'installment-type.instant-payment-fee',
  } as const;
