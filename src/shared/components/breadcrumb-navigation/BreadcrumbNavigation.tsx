import { Typography } from '@components/typography';
import {
  Breadcrumb,
  BreadcrumbItem as BreadcrumbItemComponent,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@components/ui/breadcrumb';
import ChevronRightIcon from '@icons/chevron-right.svg?react';
import { Link } from '@tanstack/react-router';

export interface BreadcrumbNavigationItem {
  label: string;
  href?: string;
  onClick?: () => void;
  isActive?: boolean;
}

interface BreadcrumbNavigationProps {
  items: BreadcrumbNavigationItem[];
  className?: string;
}

export const BreadcrumbNavigation = ({
  items,
  className,
}: BreadcrumbNavigationProps) => {
  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {items.map((item, index) => {
          const isLast = index === items.length - 1;

          return (
            <div
              key={`breadcrumb-${item.label}-${index}`}
              className="flex items-center gap-2"
            >
              <BreadcrumbItemComponent>
                {isLast ? (
                  <BreadcrumbPage>
                    <Typography
                      variant="text-s"
                      affects="semibold"
                      className="text-primary-black"
                    >
                      {item.label}
                    </Typography>
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    {item.href ? (
                      <Link to={item.href}>
                        <Typography
                          variant="text-s"
                          affects="semibold"
                          className={
                            item.isActive
                              ? 'text-primary-black'
                              : 'text-gray-500'
                          }
                        >
                          {item.label}
                        </Typography>
                      </Link>
                    ) : (
                      <button type="button" onClick={item.onClick}>
                        <Typography
                          variant="text-s"
                          affects="semibold"
                          className={
                            item.isActive
                              ? 'text-primary-black'
                              : 'text-gray-500'
                          }
                        >
                          {item.label}
                        </Typography>
                      </button>
                    )}
                  </BreadcrumbLink>
                )}
              </BreadcrumbItemComponent>

              {!isLast && (
                <BreadcrumbSeparator>
                  <ChevronRightIcon className="h-4 w-4" />
                </BreadcrumbSeparator>
              )}
            </div>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
};
