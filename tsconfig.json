{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["ESNext", "DOM", "DOM.Iterable", "ES2020"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "types": ["vitest/globals", "@testing-library/jest-dom"],
    /* Linting */
    "strict": true,
    "allowJs": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@config/*": ["./src/shared/config/*"],
      "@pages/*": ["./src/pages/*"],
      "@routes/*": ["./src/routes/*"],
      "@components/*": ["./src/shared/components/*"],
      "@icons/*": ["./src/shared/assets/icons/*"],
      "@lib/*": ["./src/shared/lib/*"],
      "@utils/*": ["./src/shared/utils/*"],
      "@hooks/*": ["./src/shared/hooks/*"],
      "@features/*": ["./src/features/*"],
      "@app": ["./src/app"],
      "@entities/*": ["./src/entities/*"],
      "@widgets/*": ["./src/widgets/*"],
      "@/*": ["./src/*"],
      "/*": ["./*"]
    }
  },
  "include": [
    "postcss.config.js",
    "commitlint.config.ts",
    "tailwind.config.ts",
    "codegen.ts",
    "src",
    "tests",
    "global.d.ts",
    "src/types.d.ts"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}
